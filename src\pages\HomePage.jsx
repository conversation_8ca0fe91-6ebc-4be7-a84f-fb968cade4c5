import {useNavigate} from "react-router-dom";
import {<PERSON>, <PERSON><PERSON><PERSON>, Moon, Sun} from "lucide-react";
import {useState, useEffect} from "react";

const HomePage = () => {
  const navigate = useNavigate();
  const [isDarkMode, setIsDarkMode] = useState(true);

  useEffect(() => {
    // Apply theme to document
    if (isDarkMode) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
  }, [isDarkMode]);

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
  };

  return (
    <div
      className={`min-h-screen w-full relative overflow-hidden transition-colors duration-300 ${
        isDarkMode
          ? "bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900"
          : "bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50"
      }`}
    >
      {/* Animated Background Stars */}
      <div className="absolute inset-0">
        {[...Array(100)].map((_, i) => (
          <div
            key={i}
            className="absolute animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 3}s`,
            }}
          >
            <div
              className={`w-1 h-1 rounded-full ${
                isDarkMode ? "bg-yellow-400" : "bg-purple-400"
              }`}
            ></div>
          </div>
        ))}
      </div>

      {/* Header */}
      <header className="absolute top-0 left-0 right-0 z-10 flex justify-between items-center p-6">
        <div
          className={`text-2xl font-bold ${
            isDarkMode ? "text-white" : "text-gray-900"
          }`}
        >
          AniPlay
        </div>
        <button
          onClick={toggleTheme}
          className={`p-3 rounded-full transition-all duration-300 ${
            isDarkMode
              ? "bg-gray-800/80 text-white hover:bg-gray-700/80 backdrop-blur-sm"
              : "bg-white/80 text-gray-900 hover:bg-white/90 backdrop-blur-sm shadow-lg"
          }`}
        >
          {isDarkMode ? <Sun size={20} /> : <Moon size={20} />}
        </button>
      </header>

      {/* Main Content */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen px-4">
        {/* Main Title with Neon Glow */}
        <div className="text-center mb-16">
          <div className="relative mb-8">
            {/* Background Glow Effect */}
            <div
              className={`absolute inset-0 rounded-full blur-3xl opacity-30 ${
                isDarkMode
                  ? "bg-gradient-to-r from-orange-400 via-yellow-400 to-orange-500"
                  : "bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500"
              }`}
              style={{transform: "scale(1.5)"}}
            ></div>

            <h1
              className={`relative text-6xl md:text-8xl lg:text-9xl font-bold mb-6 ${
                isDarkMode
                  ? "text-transparent bg-clip-text bg-gradient-to-r from-orange-400 via-yellow-400 to-orange-500"
                  : "text-transparent bg-clip-text bg-gradient-to-r from-purple-600 via-pink-600 to-purple-700"
              }`}
            >
              AniPlay Hub
            </h1>
          </div>
          <p
            className={`text-xl md:text-2xl mb-4 ${
              isDarkMode ? "text-gray-300" : "text-gray-700"
            }`}
          >
            Your ultimate entertainment platform for anime and manga.
          </p>
          <p
            className={`text-lg flex items-center justify-center gap-2 ${
              isDarkMode ? "text-orange-400" : "text-purple-600"
            }`}
          >
            <span className="animate-bounce">🔥</span>
            Join thousands of fans exploring and sharing content
          </p>
        </div>

        {/* Navigation Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 max-w-4xl w-full">
          {/* Anime Card */}
          <div
            onClick={() => navigate("/anime")}
            className={`group relative backdrop-blur-sm rounded-2xl p-8 cursor-pointer transform transition-all duration-300 hover:scale-105 border-2 ${
              isDarkMode
                ? "bg-gray-800/50 hover:bg-gray-700/50 border-gray-700 hover:border-orange-400"
                : "bg-white/50 hover:bg-white/70 border-gray-200 hover:border-orange-500 shadow-xl hover:shadow-2xl"
            }`}
          >
            <div className="flex flex-col items-center text-center">
              <div
                className={`mb-6 p-4 rounded-full transition-colors ${
                  isDarkMode
                    ? "bg-orange-500/20 group-hover:bg-orange-500/30"
                    : "bg-orange-500/10 group-hover:bg-orange-500/20"
                }`}
              >
                <Play
                  size={48}
                  className={`${
                    isDarkMode ? "text-orange-400" : "text-orange-600"
                  }`}
                />
              </div>
              <h3
                className={`text-2xl font-bold mb-4 transition-colors ${
                  isDarkMode
                    ? "text-white group-hover:text-orange-400"
                    : "text-gray-900 group-hover:text-orange-600"
                }`}
              >
                Anime →
              </h3>
              <p
                className={`leading-relaxed ${
                  isDarkMode ? "text-gray-300" : "text-gray-600"
                }`}
              >
                Explore new anime series with stunning visuals and captivating
                stories!
              </p>
            </div>
            {/* Hover Glow Effect */}
            <div
              className={`absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 ${
                isDarkMode
                  ? "bg-gradient-to-r from-orange-400/0 via-orange-400/5 to-orange-400/0"
                  : "bg-gradient-to-r from-orange-500/0 via-orange-500/10 to-orange-500/0"
              }`}
            ></div>
          </div>

          {/* Manga Card */}
          <div
            onClick={() => navigate("/manga")}
            className={`group relative backdrop-blur-sm rounded-2xl p-8 cursor-pointer transform transition-all duration-300 hover:scale-105 border-2 ${
              isDarkMode
                ? "bg-gray-800/50 hover:bg-gray-700/50 border-gray-700 hover:border-blue-400"
                : "bg-white/50 hover:bg-white/70 border-gray-200 hover:border-blue-500 shadow-xl hover:shadow-2xl"
            }`}
          >
            <div className="flex flex-col items-center text-center">
              <div
                className={`mb-6 p-4 rounded-full transition-colors ${
                  isDarkMode
                    ? "bg-blue-500/20 group-hover:bg-blue-500/30"
                    : "bg-blue-500/10 group-hover:bg-blue-500/20"
                }`}
              >
                <BookOpen
                  size={48}
                  className={`${
                    isDarkMode ? "text-blue-400" : "text-blue-600"
                  }`}
                />
              </div>
              <h3
                className={`text-2xl font-bold mb-4 transition-colors ${
                  isDarkMode
                    ? "text-white group-hover:text-blue-400"
                    : "text-gray-900 group-hover:text-blue-600"
                }`}
              >
                Manga →
              </h3>
              <p
                className={`leading-relaxed ${
                  isDarkMode ? "text-gray-300" : "text-gray-600"
                }`}
              >
                Discover amazing manga with beautiful artwork and compelling
                storylines!
              </p>
            </div>
            {/* Hover Glow Effect */}
            <div
              className={`absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 ${
                isDarkMode
                  ? "bg-gradient-to-r from-blue-400/0 via-blue-400/5 to-blue-400/0"
                  : "bg-gradient-to-r from-blue-500/0 via-blue-500/10 to-blue-500/0"
              }`}
            ></div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer
        className={`absolute bottom-0 left-0 right-0 z-10 text-center py-6 ${
          isDarkMode ? "text-gray-400" : "text-gray-600"
        }`}
      >
        <p>&copy; 2024 AniPlay Hub. Made with ❤️ for anime and manga fans.</p>
      </footer>
    </div>
  );
};

export default HomePage;
