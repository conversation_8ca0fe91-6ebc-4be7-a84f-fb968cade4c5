import React from 'react';

const LoadingSpinner = ({ size = 'md', color = 'orange', message = 'Loading...' }) => {
  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-16 w-16',
    lg: 'h-32 w-32'
  };

  const colorClasses = {
    orange: 'border-orange-400',
    blue: 'border-blue-400',
    purple: 'border-purple-400',
    white: 'border-white'
  };

  return (
    <div className="flex flex-col items-center justify-center">
      <div
        className={`animate-spin rounded-full border-b-2 ${sizeClasses[size]} ${colorClasses[color]} mb-4`}
      ></div>
      {message && (
        <p className="text-white text-lg">{message}</p>
      )}
    </div>
  );
};

export default LoadingSpinner;
