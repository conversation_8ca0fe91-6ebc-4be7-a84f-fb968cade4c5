import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, ChevronLeft, ChevronRight, ZoomIn, ZoomOut } from 'lucide-react';
import { mangaService } from '../services/mangaService';

const MangaReaderPage = () => {
  const { chapterId } = useParams();
  const navigate = useNavigate();
  const [chapterData, setChapterData] = useState(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [zoom, setZoom] = useState(1);

  useEffect(() => {
    fetchChapterData();
  }, [chapterId]);

  const fetchChapterData = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await mangaService.getChapterPages(chapterId);
      setChapterData(data);
      setCurrentPage(0);
    } catch (error) {
      console.error('Error fetching chapter data:', error);
      setError('Failed to load chapter. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const nextPage = () => {
    if (chapterData?.pages && currentPage < chapterData.pages.length - 1) {
      setCurrentPage(currentPage + 1);
    }
  };

  const prevPage = () => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);
    }
  };

  const zoomIn = () => {
    setZoom(Math.min(zoom + 0.25, 3));
  };

  const zoomOut = () => {
    setZoom(Math.max(zoom - 0.25, 0.5));
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-400 mx-auto mb-4"></div>
          <p className="text-white text-xl">Loading chapter...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-400 text-xl mb-4">{error}</p>
          <button
            onClick={() => navigate(-1)}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  const pages = chapterData?.pages || [];

  return (
    <div className="min-h-screen bg-black">
      {/* Header */}
      <header className="absolute top-0 left-0 right-0 z-50 flex items-center justify-between p-4 bg-gradient-to-b from-black/80 to-transparent">
        <div className="flex items-center gap-4">
          <button
            onClick={() => navigate(-1)}
            className="p-2 rounded-full bg-gray-800/80 text-white hover:bg-gray-700/80 transition-colors"
          >
            <ArrowLeft size={20} />
          </button>
          <h1 className="text-white font-semibold">
            {chapterData?.title || `Chapter ${chapterId}`}
          </h1>
        </div>
        
        <div className="flex items-center gap-2">
          <span className="text-white text-sm">
            {currentPage + 1} / {pages.length}
          </span>
        </div>
      </header>

      {/* Controls */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-50 flex items-center gap-4 bg-black/80 rounded-full px-6 py-3">
        <button
          onClick={prevPage}
          disabled={currentPage === 0}
          className="p-2 rounded-full bg-gray-700 text-white hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronLeft size={20} />
        </button>
        
        <button
          onClick={zoomOut}
          className="p-2 rounded-full bg-gray-700 text-white hover:bg-gray-600 transition-colors"
        >
          <ZoomOut size={20} />
        </button>
        
        <span className="text-white text-sm px-2">
          {Math.round(zoom * 100)}%
        </span>
        
        <button
          onClick={zoomIn}
          className="p-2 rounded-full bg-gray-700 text-white hover:bg-gray-600 transition-colors"
        >
          <ZoomIn size={20} />
        </button>
        
        <button
          onClick={nextPage}
          disabled={currentPage === pages.length - 1}
          className="p-2 rounded-full bg-gray-700 text-white hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronRight size={20} />
        </button>
      </div>

      {/* Manga Reader */}
      <div className="flex items-center justify-center min-h-screen p-4 pt-20 pb-20">
        {pages.length > 0 ? (
          <div className="relative overflow-auto max-h-screen">
            <img
              src={pages[currentPage]?.img || pages[currentPage]}
              alt={`Page ${currentPage + 1}`}
              className="max-w-none h-auto"
              style={{ 
                transform: `scale(${zoom})`,
                transformOrigin: 'center top'
              }}
              onError={(e) => {
                e.target.src = 'https://via.placeholder.com/800x1200?text=Page+Not+Found';
              }}
            />
          </div>
        ) : (
          <div className="text-center">
            <p className="text-white text-xl mb-4">No pages available</p>
            <button
              onClick={() => navigate(-1)}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              Go Back
            </button>
          </div>
        )}
      </div>

      {/* Navigation Areas (Click to navigate) */}
      <div className="absolute inset-0 flex">
        <div
          className="w-1/2 h-full cursor-pointer"
          onClick={prevPage}
        ></div>
        <div
          className="w-1/2 h-full cursor-pointer"
          onClick={nextPage}
        ></div>
      </div>
    </div>
  );
};

export default MangaReaderPage;
